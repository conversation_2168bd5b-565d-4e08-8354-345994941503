#if (defined PICO_RP2350) || (defined PICO_RP2040)
// -------------------------------------------------- //
// This file is autogenerated by pioasm; do not edit! //
// -------------------------------------------------- //

#pragma once

#if !PICO_NO_HARDWARE
#include "hardware/pio.h"
#endif

// --------- //
// audio_i2s //
// --------- //

#define audio_i2s_wrap_target 0
#define audio_i2s_wrap 7

#define audio_i2s_offset_entry_point 7u

static const uint16_t audio_i2s_program_instructions[] = {
            //     .wrap_target
    0x7001, //  0: out    pins, 1         side 2     
    0x1840, //  1: jmp    x--, 0          side 3     
    0x6001, //  2: out    pins, 1         side 0     
    0xe82e, //  3: set    x, 14           side 1     
    0x6001, //  4: out    pins, 1         side 0     
    0x0844, //  5: jmp    x--, 4          side 1     
    0x7001, //  6: out    pins, 1         side 2     
    0xf82e, //  7: set    x, 14           side 3     
            //     .wrap
};

#if !PICO_NO_HARDWARE
static const struct pio_program audio_i2s_program = {
    .instructions = audio_i2s_program_instructions,
    .length = 8,
    .origin = -1,
};

static inline pio_sm_config audio_i2s_program_get_default_config(uint offset) {
    pio_sm_config c = pio_get_default_sm_config();
    sm_config_set_wrap(&c, offset + audio_i2s_wrap_target, offset + audio_i2s_wrap);
    sm_config_set_sideset(&c, 2, false, false);
    return c;
}
#endif

// ----------------- //
// audio_i2s_swapped //
// ----------------- //

#define audio_i2s_swapped_wrap_target 0
#define audio_i2s_swapped_wrap 7

#define audio_i2s_swapped_offset_entry_point 7u

static const uint16_t audio_i2s_swapped_program_instructions[] = {
            //     .wrap_target
    0x6801, //  0: out    pins, 1         side 1     
    0x1840, //  1: jmp    x--, 0          side 3     
    0x6001, //  2: out    pins, 1         side 0     
    0xf02e, //  3: set    x, 14           side 2     
    0x6001, //  4: out    pins, 1         side 0     
    0x1044, //  5: jmp    x--, 4          side 2     
    0x6801, //  6: out    pins, 1         side 1     
    0xf82e, //  7: set    x, 14           side 3     
            //     .wrap
};

#if !PICO_NO_HARDWARE
static const struct pio_program audio_i2s_swapped_program = {
    .instructions = audio_i2s_swapped_program_instructions,
    .length = 8,
    .origin = -1,
};

static inline pio_sm_config audio_i2s_swapped_program_get_default_config(uint offset) {
    pio_sm_config c = pio_get_default_sm_config();
    sm_config_set_wrap(&c, offset + audio_i2s_swapped_wrap_target, offset + audio_i2s_swapped_wrap);
    sm_config_set_sideset(&c, 2, false, false);
    return c;
}

static inline void audio_i2s_program_init(PIO pio, uint sm, uint offset, uint data_pin, uint clock_pin_base) {
    pio_sm_config sm_config = audio_i2s_program_get_default_config(offset);
    sm_config_set_out_pins(&sm_config, data_pin, 1);
    sm_config_set_sideset_pins(&sm_config, clock_pin_base);
    sm_config_set_out_shift(&sm_config, false, true, 32);
    sm_config_set_fifo_join(&sm_config, PIO_FIFO_JOIN_TX);
    pio_sm_init(pio, sm, offset, &sm_config);
#if PICO_PIO_USE_GPIO_BASE
    uint64_t pin_mask = (1ull << data_pin) | (3ull << clock_pin_base);
    pio_sm_set_pindirs_with_mask64(pio, sm, pin_mask, pin_mask);
#else
    uint32_t pin_mask = (1u << data_pin) | (3u << clock_pin_base);
    pio_sm_set_pindirs_with_mask(pio, sm, pin_mask, pin_mask);
#endif
    pio_sm_set_pins(pio, sm, 0); // clear pins
    pio_sm_exec(pio, sm, pio_encode_jmp(offset + audio_i2s_offset_entry_point));
}

#endif
#endif